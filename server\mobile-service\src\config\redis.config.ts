/**
 * Redis配置
 * 
 * 提供Redis连接和缓存配置
 */

import { ConfigService } from '@nestjs/config';
import { RedisOptions } from 'ioredis';

/**
 * 获取Redis配置
 */
export const getRedisConfig = (configService: ConfigService): RedisOptions => {
  return {
    host: configService.get<string>('REDIS_HOST', 'localhost'),
    port: configService.get<number>('REDIS_PORT', 6379),
    password: configService.get<string>('REDIS_PASSWORD'),
    db: configService.get<number>('REDIS_DB', 0),
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
    lazyConnect: true,
    keepAlive: 30000,
    family: 4,
    keyPrefix: configService.get<string>('REDIS_KEY_PREFIX', 'mobile:'),
  };
};

/**
 * 缓存配置
 */
export const getCacheConfig = (configService: ConfigService) => {
  return {
    ttl: configService.get<number>('CACHE_TTL', 300), // 5分钟
    max: configService.get<number>('CACHE_MAX_ITEMS', 1000),
    isGlobal: true,
  };
};
